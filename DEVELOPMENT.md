# Development Guide

This guide provides detailed information for developers working on the Jupyter Notebook to Word Converter project.

## Architecture Overview

### Backend (FastAPI)
```
backend/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── converter/           # Core conversion logic
│   │   ├── notebook_converter.py    # Main converter class
│   │   ├── markdown_processor.py    # Markdown to Word conversion
│   │   ├── code_formatter.py        # Code syntax highlighting
│   │   └── output_processor.py      # Output handling (text, images, etc.)
│   └── models/              # Pydantic models
├── tests/                   # Backend tests
└── requirements.txt         # Python dependencies
```

### Frontend (React + TypeScript)
```
frontend/
├── src/
│   ├── components/          # React components
│   │   ├── FileUploader.tsx      # Main upload component
│   │   ├── LoadingSpinner.tsx    # Loading indicator
│   │   ├── SuccessMessage.tsx    # Success feedback
│   │   └── ErrorMessage.tsx      # Error handling
│   ├── services/            # API communication
│   └── App.tsx              # Main application component
├── public/                  # Static assets
└── package.json             # Node.js dependencies
```

## Development Setup

### Prerequisites
- Python 3.9+
- Node.js 16+
- Git

### Quick Setup
```bash
# Clone the repository
git clone <repository-url>
cd ipynbtoword

# Run the setup script
python setup.py
```

### Manual Setup

#### Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

#### Frontend
```bash
cd frontend
npm install
```

## Running the Application

### Development Mode

#### Backend
```bash
cd backend
source venv/bin/activate
uvicorn app.main:app --reload --port 8000
```

#### Frontend
```bash
cd frontend
npm start
```

### Production Mode (Docker)
```bash
docker-compose up --build
```

## Testing

### Backend Tests
```bash
cd backend
source venv/bin/activate
pytest tests/ -v --cov=app
```

### Frontend Tests
```bash
cd frontend
npm test
npm run test:coverage
```

## Code Style and Quality

### Backend (Python)
- Follow PEP 8 style guidelines
- Use type hints for all functions
- Document functions with docstrings
- Use async/await for I/O operations

### Frontend (TypeScript)
- Use TypeScript strict mode
- Follow React best practices
- Use functional components with hooks
- Implement proper error boundaries

### Linting and Formatting
```bash
# Backend
cd backend
black app/ tests/
flake8 app/ tests/
mypy app/

# Frontend
cd frontend
npm run lint
npm run format
```

## API Documentation

The backend automatically generates OpenAPI documentation available at:
- Development: http://localhost:8000/docs
- Interactive docs: http://localhost:8000/redoc

### Key Endpoints

#### POST /api/convert
Convert a Jupyter notebook to Word format.

**Request:**
- Content-Type: multipart/form-data
- Body: file (Jupyter notebook .ipynb file)

**Response:**
- Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document
- Body: Word document binary data

#### GET /api/health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "message": "Service is running"
}
```

## Adding New Features

### Backend Features

1. **New Output Type Support**
   - Extend `OutputProcessor` class
   - Add new processing method
   - Update tests

2. **New Cell Type Support**
   - Extend `NotebookConverter` class
   - Add processing logic
   - Update tests

### Frontend Features

1. **New UI Components**
   - Create component in `src/components/`
   - Add TypeScript interfaces
   - Include in main App component

2. **New API Integration**
   - Extend `src/services/api.ts`
   - Add error handling
   - Update components

## Debugging

### Backend Debugging
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
uvicorn app.main:app --reload --log-level debug

# Use debugger
import pdb; pdb.set_trace()
```

### Frontend Debugging
```bash
# Enable React Developer Tools
# Use browser developer tools
# Check console for errors
```

## Performance Considerations

### Backend
- Use async/await for file operations
- Implement proper error handling
- Consider memory usage for large files
- Add request timeouts

### Frontend
- Implement file size limits
- Show progress indicators
- Handle large file uploads
- Optimize bundle size

## Security Considerations

### File Upload Security
- Validate file types and sizes
- Sanitize file names
- Implement virus scanning (production)
- Use temporary files with cleanup

### API Security
- Implement rate limiting
- Add CORS configuration
- Validate all inputs
- Use HTTPS in production

## Deployment

### Docker Deployment
```bash
# Build and run
docker-compose up --build

# Production deployment
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment

#### Backend
```bash
# Install dependencies
pip install -r requirements.txt

# Run with Gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

#### Frontend
```bash
# Build for production
npm run build

# Serve with nginx or similar
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Commit Message Format
```
type(scope): description

- feat: new feature
- fix: bug fix
- docs: documentation
- style: formatting
- refactor: code restructuring
- test: adding tests
- chore: maintenance
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Check virtual environment activation
   - Verify dependencies installation

2. **CORS Errors**
   - Check backend CORS configuration
   - Verify frontend API URL

3. **File Upload Failures**
   - Check file size limits
   - Verify file format
   - Check server logs

4. **Conversion Errors**
   - Validate notebook format
   - Check for unsupported features
   - Review error logs

### Getting Help

- Check the GitHub issues
- Review the documentation
- Contact the development team
