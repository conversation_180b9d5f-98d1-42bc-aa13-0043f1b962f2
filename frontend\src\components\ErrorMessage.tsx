import React from 'react';

interface ErrorMessageProps {
  message: string;
  onReset: () => void;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  onReset,
}) => {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-6">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800">Conversion Failed</h3>
          <p className="text-sm text-red-700 mt-1">{message}</p>
          
          <div className="mt-4">
            <button
              onClick={onReset}
              className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              Try Again
            </button>
          </div>
          
          <div className="mt-4 text-xs text-red-600">
            <p className="font-medium">Common issues:</p>
            <ul className="mt-1 space-y-1">
              <li>• Make sure the file is a valid Jupyter notebook (.ipynb)</li>
              <li>• Check that the file is not corrupted</li>
              <li>• Ensure the file size is under 50MB</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
