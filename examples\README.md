# Example Notebooks

This directory contains example Jupyter notebooks that demonstrate the conversion capabilities of the ipynb-to-word converter.

## Files

### sample_notebook.ipynb
A comprehensive example notebook that showcases:

- **Markdown formatting**: Headers, bold, italic, lists, inline code
- **Code cells**: Python code with syntax highlighting
- **Output displays**: Text output, HTML tables, execution results
- **Error handling**: Error messages with tracebacks

This notebook is designed to test all major features of the converter and serves as a good example of what the converted Word document will look like.

## Usage

1. Start the application (see main README.md for instructions)
2. Upload `sample_notebook.ipynb` through the web interface
3. Download the converted Word document
4. Compare the original notebook with the converted document to see the formatting preservation

## Expected Output

The converted Word document should include:

- Properly formatted headings and text
- Syntax-highlighted code blocks
- Preserved output formatting
- Error messages in red text
- Execution numbers for code cells
- Professional document styling

## Creating Your Own Examples

When creating test notebooks, include:

1. Various markdown elements (headers, lists, formatting)
2. Code in different languages (if supported)
3. Different types of outputs (text, images, plots)
4. Error cases to test error handling
5. Complex formatting to test edge cases
