{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Analysis Example\n", "\n", "This notebook demonstrates various features that will be converted to Word format.\n", "\n", "## Introduction\n", "\n", "This is a **sample notebook** that showcases:\n", "\n", "- Markdown formatting\n", "- Code cells with syntax highlighting\n", "- Output displays\n", "- Error handling\n", "\n", "### Mathematical Expressions\n", "\n", "Here's some `inline code` and a formula: E = mc²\n", "\n", "## Data Processing"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset created with 100 samples\n", "Features: ['feature_1', 'feature_2', 'target']\n"]}], "source": ["# Create sample dataset\n", "n_samples = 100\n", "data = {\n", "    'feature_1': np.random.normal(0, 1, n_samples),\n", "    'feature_2': np.random.exponential(2, n_samples),\n", "    'target': np.random.choice([0, 1], n_samples)\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "print(f\"Dataset created with {len(df)} samples\")\n", "print(f\"Features: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature_1</th>\n", "      <th>feature_2</th>\n", "      <th>target</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.496714</td>\n", "      <td>1.234567</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.138264</td>\n", "      <td>2.345678</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   feature_1  feature_2  target\n", "0   0.496714   1.234567       1\n", "1  -0.138264   2.345678       0"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display first few rows\n", "df.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Summary\n", "\n", "Let's examine the basic statistics of our dataset:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    100.000000\n", "mean       0.123456\n", "std        0.987654\n", "min       -2.345678\n", "25%       -0.567890\n", "50%        0.123456\n", "75%        0.789012\n", "max        2.345678\n", "Name: feature_1, dtype: float64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Statistical summary\n", "df['feature_1'].describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Error Handling Example\n", "\n", "This cell demonstrates how errors are handled in the conversion:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "ZeroDivisionError", "evalue": "division by zero", "output_type": "error", "traceback": ["\\u001b[0;31m---------------------------------------------------------------------------\\u001b[0m", "\\u001b[0;31mZeroDivisionError\\u001b[0m                         Traceback (most recent call last)", "\\u001b[0;32m<ipython-input-5-9e1622b385b6>\\u001b[0m in \\u001b[0;36m<module>\\u001b[0;34m\\u001b[0m\n\\u001b[1;32m      1\\u001b[0m \\u001b[0;31m# This will cause an error\\u001b[0m\\u001b[0;34m\\u001b[0m\\u001b[0;34m\\u001b[0m\\u001b[0;34m\\u001b[0m\\u001b[0m\n\\u001b[0;32m----> 2\\u001b[0;31m \\u001b[0mresult\\u001b[0m \\u001b[0;34m=\\u001b[0m \\u001b[0;36m1\\u001b[0m \\u001b[0;34m/\\u001b[0m \\u001b[0;36m0\\u001b[0m\\u001b[0;34m\\u001b[0m\\u001b[0;34m\\u001b[0m\\u001b[0m\n\\u001b[0m", "\\u001b[0;31mZeroDivisionError\\u001b[0m: division by zero"]}], "source": ["# This will cause an error\n", "result = 1 / 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook demonstrates various cell types and outputs that should be properly converted to Word format:\n", "\n", "1. **Markdown cells** with rich formatting\n", "2. **Code cells** with syntax highlighting\n", "3. **Output displays** including text and HTML\n", "4. **Error messages** with tracebacks\n", "\n", "The converted Word document should preserve all this formatting while maintaining readability."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}