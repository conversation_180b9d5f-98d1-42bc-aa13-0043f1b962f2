import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useMutation } from 'react-query';
import { convertNotebook } from '../services/api';
import { LoadingSpinner } from './LoadingSpinner';
import { SuccessMessage } from './SuccessMessage';
import { ErrorMessage } from './ErrorMessage';

interface ConversionState {
  isConverting: boolean;
  success: boolean;
  error: string | null;
  downloadUrl: string | null;
  filename: string | null;
}

export const FileUploader: React.FC = () => {
  const [conversionState, setConversionState] = useState<ConversionState>({
    isConverting: false,
    success: false,
    error: null,
    downloadUrl: null,
    filename: null,
  });

  const convertMutation = useMutation(convertNotebook, {
    onMutate: () => {
      setConversionState({
        isConverting: true,
        success: false,
        error: null,
        downloadUrl: null,
        filename: null,
      });
    },
    onSuccess: (data) => {
      const url = URL.createObjectURL(data.blob);
      setConversionState({
        isConverting: false,
        success: true,
        error: null,
        downloadUrl: url,
        filename: data.filename,
      });
    },
    onError: (error: any) => {
      setConversionState({
        isConverting: false,
        success: false,
        error: error.response?.data?.detail || 'Conversion failed. Please try again.',
        downloadUrl: null,
        filename: null,
      });
    },
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      if (!file.name.endsWith('.ipynb')) {
        setConversionState({
          isConverting: false,
          success: false,
          error: 'Please select a valid Jupyter notebook (.ipynb) file.',
          downloadUrl: null,
          filename: null,
        });
        return;
      }
      convertMutation.mutate(file);
    }
  }, [convertMutation]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/json': ['.ipynb'],
    },
    multiple: false,
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const handleDownload = () => {
    if (conversionState.downloadUrl && conversionState.filename) {
      const link = document.createElement('a');
      link.href = conversionState.downloadUrl;
      link.download = conversionState.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const resetState = () => {
    if (conversionState.downloadUrl) {
      URL.revokeObjectURL(conversionState.downloadUrl);
    }
    setConversionState({
      isConverting: false,
      success: false,
      error: null,
      downloadUrl: null,
      filename: null,
    });
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
          ${isDragActive 
            ? 'border-primary-500 bg-primary-50' 
            : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
          }
          ${conversionState.isConverting ? 'pointer-events-none opacity-50' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-4">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          
          <div>
            <p className="text-lg font-medium text-gray-900">
              {isDragActive ? 'Drop your notebook here' : 'Upload Jupyter Notebook'}
            </p>
            <p className="text-gray-500 mt-1">
              Drag and drop your .ipynb file here, or click to browse
            </p>
          </div>
          
          <div className="text-sm text-gray-400">
            Maximum file size: 50MB
          </div>
        </div>
      </div>

      {/* Status Messages */}
      <div className="mt-6 space-y-4">
        {conversionState.isConverting && (
          <LoadingSpinner message="Converting your notebook..." />
        )}
        
        {conversionState.success && (
          <SuccessMessage 
            message="Conversion completed successfully!"
            onDownload={handleDownload}
            onReset={resetState}
            filename={conversionState.filename}
          />
        )}
        
        {conversionState.error && (
          <ErrorMessage 
            message={conversionState.error}
            onReset={resetState}
          />
        )}
      </div>

      {/* File Format Info */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Supported Features</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Code cells with syntax highlighting</li>
          <li>• Markdown formatting (headers, lists, bold, italic)</li>
          <li>• Text and image outputs</li>
          <li>• Error messages and tracebacks</li>
          <li>• Execution numbers and metadata</li>
        </ul>
      </div>
    </div>
  );
};
