import React from 'react';

interface SuccessMessageProps {
  message: string;
  filename: string | null;
  onDownload: () => void;
  onReset: () => void;
}

export const SuccessMessage: React.FC<SuccessMessageProps> = ({
  message,
  filename,
  onDownload,
  onReset,
}) => {
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <div className="flex-1">
          <h3 className="text-sm font-medium text-green-800">{message}</h3>
          {filename && (
            <p className="text-sm text-green-700 mt-1">
              File: {filename}
            </p>
          )}
          
          <div className="mt-4 flex space-x-3">
            <button
              onClick={onDownload}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download Word Document
            </button>
            
            <button
              onClick={onReset}
              className="inline-flex items-center px-4 py-2 bg-white text-green-700 text-sm font-medium rounded-md border border-green-300 hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
            >
              Convert Another File
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
