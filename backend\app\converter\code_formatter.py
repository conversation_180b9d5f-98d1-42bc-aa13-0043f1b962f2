"""
Code formatting with syntax highlighting for notebook cells.
"""

from docx import Document
from docx.shared import RGBColor, Pt
from pygments import highlight
from pygments.lexers import get_lexer_by_name, guess_lexer
from pygments.formatters import get_formatter_by_name
from pygments.token import Token
import logging

logger = logging.getLogger(__name__)


class CodeFormatter:
    """Formats code with syntax highlighting for Word documents."""
    
    def __init__(self):
        # Color scheme for syntax highlighting
        self.token_colors = {
            Token.Keyword: RGBColor(0, 0, 255),           # Blue
            Token.String: RGBColor(163, 21, 21),          # Dark red
            Token.Comment: RGBColor(0, 128, 0),           # Green
            Token.Number: RGBColor(128, 0, 128),          # Purple
            Token.Operator: RGBColor(0, 0, 0),            # Black
            Token.Name.Function: RGBColor(128, 0, 0),     # Dark red
            Token.Name.Class: RGBColor(0, 128, 128),      # Teal
            Token.Name.Builtin: RGBColor(128, 0, 128),    # Purple
        }
    
    async def format_code(self, doc: Document, code: str, language: str = 'python') -> None:
        """
        Format code with syntax highlighting and add to document.
        
        Args:
            doc: Word document object
            code: Code text to format
            language: Programming language for syntax highlighting
        """
        try:
            # Get lexer for the language
            try:
                lexer = get_lexer_by_name(language)
            except:
                # Fallback to guessing the language
                try:
                    lexer = guess_lexer(code)
                except:
                    # Ultimate fallback to plain text
                    lexer = get_lexer_by_name('text')
            
            # Tokenize the code
            tokens = list(lexer.get_tokens(code))
            
            # Create paragraph for code
            para = doc.add_paragraph()
            para.style = 'Code'
            
            # Add each token with appropriate formatting
            for token_type, text in tokens:
                if text.strip():  # Skip whitespace-only tokens for color formatting
                    run = para.add_run(text)
                    run.font.name = 'Consolas'
                    run.font.size = Pt(10)
                    
                    # Apply color based on token type
                    color = self._get_token_color(token_type)
                    if color:
                        run.font.color.rgb = color
                else:
                    # Preserve whitespace
                    para.add_run(text)
            
        except Exception as e:
            logger.warning(f"Failed to apply syntax highlighting: {e}")
            # Fallback to plain code formatting
            para = doc.add_paragraph(code)
            para.style = 'Code'
    
    def _get_token_color(self, token_type) -> RGBColor:
        """Get color for a specific token type."""
        # Check for exact match first
        if token_type in self.token_colors:
            return self.token_colors[token_type]
        
        # Check for parent token types
        for parent_type, color in self.token_colors.items():
            if token_type in parent_type:
                return color
        
        # Default color (black)
        return RGBColor(0, 0, 0)
