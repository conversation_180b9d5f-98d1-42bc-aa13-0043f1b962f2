# Jupyter Notebook to Word Converter

A comprehensive web application that converts Jupyter notebook (.ipynb) files to Microsoft Word (.docx) format while preserving formatting, syntax highlighting, and outputs.

## Features

- **Complete Format Preservation**: Maintains code cell formatting with syntax highlighting
- **Rich Content Support**: Preserves markdown formatting (headers, bold, italic, lists, etc.)
- **Output Handling**: Includes text outputs, images, and plots in the converted document
- **User-Friendly Interface**: Drag-and-drop file upload with instant download
- **Robust Error Handling**: Validates files and handles corrupted notebooks gracefully

## Technology Stack

### Backend
- **FastAPI**: Modern Python web framework for building APIs
- **nbformat**: Official Jupyter notebook format parser
- **python-docx**: Word document generation library
- **Pygments**: Syntax highlighting for code cells
- **Pillow**: Image processing and handling

### Frontend
- **React + TypeScript**: Modern component-based UI with type safety
- **Tailwind CSS**: Utility-first CSS framework
- **React Dropzone**: Drag-and-drop file upload
- **Axios**: HTTP client for API communication

## Project Structure

```
ipynbtoword/
├── backend/                 # FastAPI backend service
│   ├── app/
│   │   ├── main.py         # FastAPI application entry point
│   │   ├── converter/      # Conversion logic
│   │   ├── models/         # Data models
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile          # Backend container
├── frontend/               # React frontend application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   ├── package.json        # Node.js dependencies
│   └── Dockerfile          # Frontend container
├── examples/               # Example notebooks and conversions
├── docker-compose.yml      # Multi-container setup
└── README.md              # This file
```

## Quick Start

### Prerequisites
- Python 3.9+
- Node.js 16+
- Docker (optional, for containerized deployment)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ipynbtoword
   ```

2. **Backend Setup**
   ```bash
   cd backend
   pip install -r requirements.txt
   uvicorn app.main:app --reload --port 8000
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm start
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Docker Deployment

```bash
docker-compose up --build
```

## API Endpoints

- `POST /api/convert` - Upload .ipynb file and get .docx conversion
- `GET /api/health` - Health check endpoint
- `GET /docs` - Interactive API documentation

## Usage

1. Open the web interface at http://localhost:3000
2. Drag and drop your .ipynb file or click to browse
3. Wait for the conversion to complete
4. Download the generated .docx file

## Supported Features

### Input Formats
- Jupyter Notebook (.ipynb) files
- All notebook versions (v4+)

### Cell Types
- **Code Cells**: Syntax highlighted with execution numbers
- **Markdown Cells**: Full markdown formatting support
- **Raw Cells**: Preserved as plain text

### Output Types
- Text outputs
- HTML outputs
- Images (PNG, JPEG, SVG)
- Matplotlib/Plotly plots
- Error outputs with formatting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details
