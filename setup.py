#!/usr/bin/env python3
"""
Setup script for the Jupyter Notebook to Word Converter application.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """Run a shell command and handle errors."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_requirements():
    """Check if required tools are installed."""
    print("Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        sys.exit(1)
    
    # Check if Node.js is installed
    result = run_command("node --version", check=False)
    if result.returncode != 0:
        print("Error: Node.js is required but not found")
        print("Please install Node.js 16+ from https://nodejs.org/")
        sys.exit(1)
    
    # Check if npm is installed
    result = run_command("npm --version", check=False)
    if result.returncode != 0:
        print("Error: npm is required but not found")
        sys.exit(1)
    
    print("✓ All requirements satisfied")


def setup_backend():
    """Set up the backend environment."""
    print("\n=== Setting up Backend ===")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("Error: backend directory not found")
        sys.exit(1)
    
    # Create virtual environment
    print("Creating Python virtual environment...")
    run_command("python -m venv venv", cwd=backend_dir)
    
    # Determine activation script path
    if os.name == 'nt':  # Windows
        activate_script = backend_dir / "venv" / "Scripts" / "activate"
        pip_path = backend_dir / "venv" / "Scripts" / "pip"
    else:  # Unix/Linux/macOS
        activate_script = backend_dir / "venv" / "bin" / "activate"
        pip_path = backend_dir / "venv" / "bin" / "pip"
    
    # Install dependencies
    print("Installing Python dependencies...")
    run_command(f"{pip_path} install --upgrade pip", cwd=backend_dir)
    run_command(f"{pip_path} install -r requirements.txt", cwd=backend_dir)
    
    print("✓ Backend setup complete")


def setup_frontend():
    """Set up the frontend environment."""
    print("\n=== Setting up Frontend ===")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("Error: frontend directory not found")
        sys.exit(1)
    
    # Install dependencies
    print("Installing Node.js dependencies...")
    run_command("npm install", cwd=frontend_dir)
    
    print("✓ Frontend setup complete")


def run_tests():
    """Run tests for both backend and frontend."""
    print("\n=== Running Tests ===")
    
    # Backend tests
    print("Running backend tests...")
    backend_dir = Path("backend")
    
    if os.name == 'nt':  # Windows
        python_path = backend_dir / "venv" / "Scripts" / "python"
    else:  # Unix/Linux/macOS
        python_path = backend_dir / "venv" / "bin" / "python"
    
    run_command(f"{python_path} -m pytest tests/ -v", cwd=backend_dir)
    
    # Frontend tests
    print("Running frontend tests...")
    frontend_dir = Path("frontend")
    run_command("npm test -- --coverage --watchAll=false", cwd=frontend_dir)
    
    print("✓ All tests passed")


def create_env_files():
    """Create environment configuration files."""
    print("\n=== Creating Environment Files ===")
    
    # Backend .env file
    backend_env = Path("backend") / ".env"
    if not backend_env.exists():
        with open(backend_env, "w") as f:
            f.write("# Backend Environment Variables\n")
            f.write("PYTHONPATH=/app\n")
            f.write("LOG_LEVEL=INFO\n")
        print("✓ Created backend/.env")
    
    # Frontend .env file
    frontend_env = Path("frontend") / ".env"
    if not frontend_env.exists():
        with open(frontend_env, "w") as f:
            f.write("# Frontend Environment Variables\n")
            f.write("REACT_APP_API_URL=http://localhost:8000\n")
            f.write("GENERATE_SOURCEMAP=false\n")
        print("✓ Created frontend/.env")


def main():
    """Main setup function."""
    print("Jupyter Notebook to Word Converter - Setup Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("README.md").exists():
        print("Error: Please run this script from the project root directory")
        sys.exit(1)
    
    try:
        check_requirements()
        create_env_files()
        setup_backend()
        setup_frontend()
        
        print("\n" + "=" * 50)
        print("✓ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Start the backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload")
        print("2. Start the frontend: cd frontend && npm start")
        print("3. Open http://localhost:3000 in your browser")
        print("\nOr use Docker: docker-compose up --build")
        
        # Ask if user wants to run tests
        response = input("\nWould you like to run tests now? (y/N): ")
        if response.lower() in ['y', 'yes']:
            run_tests()
            
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nSetup failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
