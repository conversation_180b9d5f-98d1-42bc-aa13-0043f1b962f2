"""
Tests for the main FastAPI application.
"""

import pytest
from fastapi.testclient import TestClient
import tempfile
import json
import os
from pathlib import Path

from app.main import app

client = TestClient(app)


def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/api/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "message" in data


def test_convert_endpoint_no_file():
    """Test conversion endpoint without file."""
    response = client.post("/api/convert")
    assert response.status_code == 422  # Validation error


def test_convert_endpoint_invalid_file():
    """Test conversion endpoint with invalid file type."""
    with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as temp_file:
        temp_file.write(b"This is not a notebook")
        temp_file.flush()
        
        with open(temp_file.name, "rb") as f:
            response = client.post(
                "/api/convert",
                files={"file": ("test.txt", f, "text/plain")}
            )
        
        os.unlink(temp_file.name)
    
    assert response.status_code == 400
    assert "must be a Jupyter notebook" in response.json()["detail"]


def create_sample_notebook():
    """Create a sample notebook for testing."""
    notebook = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": ["# Test Notebook\n", "This is a test notebook."]
            },
            {
                "cell_type": "code",
                "execution_count": 1,
                "metadata": {},
                "outputs": [
                    {
                        "name": "stdout",
                        "output_type": "stream",
                        "text": ["Hello, World!\n"]
                    }
                ],
                "source": ["print('Hello, World!')"]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    return notebook


def test_convert_valid_notebook():
    """Test conversion with a valid notebook."""
    notebook = create_sample_notebook()
    
    with tempfile.NamedTemporaryFile(suffix=".ipynb", delete=False) as temp_file:
        json.dump(notebook, temp_file, indent=2)
        temp_file.flush()
        
        with open(temp_file.name, "rb") as f:
            response = client.post(
                "/api/convert",
                files={"file": ("test_notebook.ipynb", f, "application/json")}
            )
        
        os.unlink(temp_file.name)
    
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    assert len(response.content) > 0  # Should have content


def test_convert_malformed_notebook():
    """Test conversion with malformed notebook."""
    malformed_notebook = {"invalid": "notebook"}
    
    with tempfile.NamedTemporaryFile(suffix=".ipynb", delete=False) as temp_file:
        json.dump(malformed_notebook, temp_file)
        temp_file.flush()
        
        with open(temp_file.name, "rb") as f:
            response = client.post(
                "/api/convert",
                files={"file": ("malformed.ipynb", f, "application/json")}
            )
        
        os.unlink(temp_file.name)
    
    assert response.status_code == 500
    assert "Conversion failed" in response.json()["detail"]


@pytest.mark.asyncio
async def test_cors_headers():
    """Test that CORS headers are properly set."""
    response = client.options("/api/health")
    # Note: TestClient doesn't fully simulate CORS, but we can check the middleware is configured
    assert response.status_code in [200, 405]  # OPTIONS might not be implemented
