"""
Tests for the notebook converter functionality.
"""

import pytest
import tempfile
import json
import os
from pathlib import Path
from docx import Document

from app.converter.notebook_converter import NotebookConverter
from app.converter.markdown_processor import MarkdownProcessor
from app.converter.code_formatter import CodeFormatter
from app.converter.output_processor import OutputProcessor


@pytest.fixture
def sample_notebook():
    """Create a sample notebook for testing."""
    return {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# Sample Notebook\n",
                    "\n",
                    "This is a **test** notebook with *various* formatting.\n",
                    "\n",
                    "## Code Example\n",
                    "\n",
                    "Here's some `inline code` and a list:\n",
                    "\n",
                    "- Item 1\n",
                    "- Item 2\n",
                    "- Item 3"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": 1,
                "metadata": {},
                "outputs": [
                    {
                        "name": "stdout",
                        "output_type": "stream",
                        "text": ["Hello, World!\n"]
                    }
                ],
                "source": [
                    "# This is a Python comment\n",
                    "print('Hello, World!')\n",
                    "x = 42\n",
                    "y = 'test string'"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": 2,
                "metadata": {},
                "outputs": [
                    {
                        "data": {
                            "text/plain": ["42"]
                        },
                        "execution_count": 2,
                        "metadata": {},
                        "output_type": "execute_result"
                    }
                ],
                "source": ["x"]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.8.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }


@pytest.mark.asyncio
async def test_notebook_converter_basic(sample_notebook):
    """Test basic notebook conversion."""
    converter = NotebookConverter()
    
    # Create temporary input file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ipynb', delete=False) as input_file:
        json.dump(sample_notebook, input_file, indent=2)
        input_path = input_file.name
    
    # Create temporary output file
    output_path = tempfile.mktemp(suffix='.docx')
    
    try:
        # Convert notebook
        await converter.convert_notebook(input_path, output_path)
        
        # Verify output file exists and has content
        assert os.path.exists(output_path)
        assert os.path.getsize(output_path) > 0
        
        # Verify it's a valid Word document
        doc = Document(output_path)
        assert len(doc.paragraphs) > 0
        
        # Check that title is present
        title_found = any("Sample Notebook" in p.text for p in doc.paragraphs)
        assert title_found
        
    finally:
        # Clean up
        if os.path.exists(input_path):
            os.unlink(input_path)
        if os.path.exists(output_path):
            os.unlink(output_path)


@pytest.mark.asyncio
async def test_markdown_processor():
    """Test markdown processing."""
    processor = MarkdownProcessor()
    doc = Document()
    
    markdown_text = """# Heading 1
    
This is a paragraph with **bold** and *italic* text.

## Heading 2

- List item 1
- List item 2

Some `inline code` here."""
    
    await processor.process_markdown(doc, markdown_text)
    
    # Check that content was added
    assert len(doc.paragraphs) > 0
    
    # Check for headings
    headings = [p for p in doc.paragraphs if p.style.name.startswith('Heading')]
    assert len(headings) >= 2


@pytest.mark.asyncio
async def test_code_formatter():
    """Test code formatting."""
    formatter = CodeFormatter()
    doc = Document()
    
    code = """def hello_world():
    print("Hello, World!")
    return 42"""
    
    await formatter.format_code(doc, code, 'python')
    
    # Check that code was added
    code_paragraphs = [p for p in doc.paragraphs if p.style.name == 'Code']
    assert len(code_paragraphs) > 0


@pytest.mark.asyncio
async def test_output_processor():
    """Test output processing."""
    processor = OutputProcessor()
    doc = Document()
    
    outputs = [
        {
            "output_type": "stream",
            "name": "stdout",
            "text": ["Hello, World!\n"]
        },
        {
            "output_type": "execute_result",
            "execution_count": 1,
            "data": {
                "text/plain": ["42"]
            },
            "metadata": {}
        }
    ]
    
    await processor.process_outputs(doc, outputs, execution_count=1)
    
    # Check that outputs were processed
    output_paragraphs = [p for p in doc.paragraphs if p.style.name == 'Output']
    assert len(output_paragraphs) > 0


def test_notebook_title_extraction():
    """Test notebook title extraction."""
    converter = NotebookConverter()
    
    # Test with metadata title
    notebook_with_title = {
        "metadata": {"title": "My Custom Title"},
        "cells": []
    }
    title = converter._get_notebook_title(notebook_with_title, "test.ipynb")
    assert title == "My Custom Title"
    
    # Test with markdown heading
    notebook_with_heading = {
        "metadata": {},
        "cells": [
            {
                "cell_type": "markdown",
                "source": ["# Main Title\n", "Some content"]
            }
        ]
    }
    title = converter._get_notebook_title(notebook_with_heading, "test.ipynb")
    assert title == "Main Title"
    
    # Test fallback to filename
    notebook_no_title = {
        "metadata": {},
        "cells": []
    }
    title = converter._get_notebook_title(notebook_no_title, "/path/to/my_notebook.ipynb")
    assert title == "my_notebook"
