import React from 'react';

interface LoadingSpinnerProps {
  message?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  message = "Loading..." 
}) => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div className="flex items-center justify-center space-x-3">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="text-blue-700 font-medium">{message}</span>
      </div>
      <div className="mt-3 text-sm text-blue-600 text-center">
        This may take a few moments depending on your notebook size...
      </div>
    </div>
  );
};
