import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5 minutes timeout for large files
});

export interface ConversionResponse {
  blob: Blob;
  filename: string;
}

export const convertNotebook = async (file: File): Promise<ConversionResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await api.post('/api/convert', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      responseType: 'blob',
    });

    // Extract filename from Content-Disposition header
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'converted_notebook.docx';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    return {
      blob: response.data,
      filename,
    };
  } catch (error) {
    console.error('Conversion error:', error);
    throw error;
  }
};

export const checkHealth = async (): Promise<{ status: string; message: string }> => {
  try {
    const response = await api.get('/api/health');
    return response.data;
  } catch (error) {
    console.error('Health check error:', error);
    throw error;
  }
};
