"""
FastAPI application for Jupyter notebook to Word conversion service.
"""

from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import tempfile
import os
from pathlib import Path
import logging

from .converter.notebook_converter import NotebookConverter
from .models.response_models import ConversionResponse, HealthResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Jupyter Notebook to Word Converter",
    description="Convert .ipynb files to .docx format with preserved formatting",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize converter
converter = NotebookConverter()


@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(status="healthy", message="Service is running")


@app.post("/api/convert")
async def convert_notebook(file: UploadFile = File(...)):
    """
    Convert uploaded Jupyter notebook to Word document.
    
    Args:
        file: Uploaded .ipynb file
        
    Returns:
        FileResponse: Generated .docx file
    """
    # Validate file type
    if not file.filename.endswith('.ipynb'):
        raise HTTPException(
            status_code=400,
            detail="File must be a Jupyter notebook (.ipynb)"
        )
    
    try:
        # Create temporary files
        with tempfile.NamedTemporaryFile(delete=False, suffix='.ipynb') as temp_input:
            # Save uploaded file
            content = await file.read()
            temp_input.write(content)
            temp_input_path = temp_input.name
        
        # Generate output filename
        base_name = Path(file.filename).stem
        temp_output_path = tempfile.mktemp(suffix='.docx')
        
        # Convert notebook to Word
        await converter.convert_notebook(temp_input_path, temp_output_path)
        
        # Clean up input file
        os.unlink(temp_input_path)
        
        # Return the converted file
        return FileResponse(
            path=temp_output_path,
            filename=f"{base_name}.docx",
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=lambda: os.unlink(temp_output_path)  # Clean up after sending
        )
        
    except Exception as e:
        logger.error(f"Conversion failed: {str(e)}")
        # Clean up files on error
        if 'temp_input_path' in locals() and os.path.exists(temp_input_path):
            os.unlink(temp_input_path)
        if 'temp_output_path' in locals() and os.path.exists(temp_output_path):
            os.unlink(temp_output_path)
            
        raise HTTPException(
            status_code=500,
            detail=f"Conversion failed: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
