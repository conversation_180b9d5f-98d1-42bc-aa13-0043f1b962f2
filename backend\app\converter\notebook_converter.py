"""
Core notebook to Word conversion logic.
"""

import nbformat
from docx import Document
from docx.shared import Inches, RGBColor, Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.style import WD_STYLE_TYPE
import logging
from typing import Dict, Any, List
import base64
import io
from PIL import Image
import tempfile
import os

from .markdown_processor import MarkdownProcessor
from .code_formatter import CodeFormatter
from .output_processor import OutputProcessor

logger = logging.getLogger(__name__)


class NotebookConverter:
    """Converts Jupyter notebooks to Word documents."""
    
    def __init__(self):
        self.markdown_processor = MarkdownProcessor()
        self.code_formatter = CodeFormatter()
        self.output_processor = OutputProcessor()
    
    async def convert_notebook(self, input_path: str, output_path: str) -> None:
        """
        Convert a Jupyter notebook to a Word document.
        
        Args:
            input_path: Path to the .ipynb file
            output_path: Path for the output .docx file
        """
        try:
            # Read the notebook
            with open(input_path, 'r', encoding='utf-8') as f:
                notebook = nbformat.read(f, as_version=4)
            
            # Create Word document
            doc = Document()
            self._setup_document_styles(doc)
            
            # Add title
            title = self._get_notebook_title(notebook, input_path)
            title_paragraph = doc.add_heading(title, level=1)
            title_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            
            # Process each cell
            for cell_index, cell in enumerate(notebook.cells):
                await self._process_cell(doc, cell, cell_index)
            
            # Save the document
            doc.save(output_path)
            logger.info(f"Successfully converted notebook to {output_path}")
            
        except Exception as e:
            logger.error(f"Error converting notebook: {str(e)}")
            raise
    
    def _setup_document_styles(self, doc: Document) -> None:
        """Set up custom styles for the document."""
        styles = doc.styles
        
        # Code style
        try:
            code_style = styles.add_style('Code', WD_STYLE_TYPE.PARAGRAPH)
            code_font = code_style.font
            code_font.name = 'Consolas'
            code_font.size = Pt(10)
            code_style.paragraph_format.left_indent = Inches(0.5)
            code_style.paragraph_format.space_before = Inches(0.1)
            code_style.paragraph_format.space_after = Inches(0.1)
        except ValueError:
            # Style already exists
            pass
        
        # Output style
        try:
            output_style = styles.add_style('Output', WD_STYLE_TYPE.PARAGRAPH)
            output_font = output_style.font
            output_font.name = 'Consolas'
            output_font.size = Pt(9)
            output_font.color.rgb = RGBColor(64, 64, 64)
            output_style.paragraph_format.left_indent = Inches(0.75)
            output_style.paragraph_format.space_before = Inches(0.05)
            output_style.paragraph_format.space_after = Inches(0.1)
        except ValueError:
            # Style already exists
            pass
    
    def _get_notebook_title(self, notebook: Dict[str, Any], input_path: str) -> str:
        """Extract or generate a title for the notebook."""
        # Try to get title from metadata
        metadata = notebook.get('metadata', {})
        if 'title' in metadata:
            return metadata['title']
        
        # Try to find first heading in markdown cells
        for cell in notebook.cells:
            if cell.cell_type == 'markdown':
                lines = cell.source.split('\n')
                for line in lines:
                    if line.startswith('#'):
                        return line.lstrip('#').strip()
        
        # Use filename as fallback
        return os.path.splitext(os.path.basename(input_path))[0]
    
    async def _process_cell(self, doc: Document, cell: Dict[str, Any], cell_index: int) -> None:
        """Process a single notebook cell."""
        cell_type = cell.get('cell_type', '')
        
        if cell_type == 'markdown':
            await self._process_markdown_cell(doc, cell)
        elif cell_type == 'code':
            await self._process_code_cell(doc, cell, cell_index)
        elif cell_type == 'raw':
            await self._process_raw_cell(doc, cell)
    
    async def _process_markdown_cell(self, doc: Document, cell: Dict[str, Any]) -> None:
        """Process a markdown cell."""
        source = cell.get('source', '')
        if source.strip():
            await self.markdown_processor.process_markdown(doc, source)
    
    async def _process_code_cell(self, doc: Document, cell: Dict[str, Any], cell_index: int) -> None:
        """Process a code cell."""
        source = cell.get('source', '')
        execution_count = cell.get('execution_count')
        
        if source.strip():
            # Add execution count label
            if execution_count is not None:
                label_para = doc.add_paragraph(f"In [{execution_count}]:")
                label_para.style = doc.styles['Heading 6']
            
            # Add code with syntax highlighting
            await self.code_formatter.format_code(doc, source)
            
            # Process outputs
            outputs = cell.get('outputs', [])
            if outputs:
                await self.output_processor.process_outputs(doc, outputs, execution_count)
    
    async def _process_raw_cell(self, doc: Document, cell: Dict[str, Any]) -> None:
        """Process a raw cell."""
        source = cell.get('source', '')
        if source.strip():
            para = doc.add_paragraph(source)
            para.style = 'Code'
