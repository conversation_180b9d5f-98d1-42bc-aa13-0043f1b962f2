"""
Output processing for notebook cell outputs.
"""

from docx import Document
from docx.shared import Inches, RGBColor
import base64
import io
from PIL import Image
import tempfile
import os
import json
import logging
from typing import List, Dict, Any
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


class OutputProcessor:
    """Processes notebook cell outputs and adds them to Word documents."""
    
    def __init__(self):
        self.max_image_width = Inches(6)
        self.max_image_height = Inches(4)
    
    async def process_outputs(self, doc: Document, outputs: List[Dict[str, Any]], execution_count: int = None) -> None:
        """
        Process all outputs from a code cell.
        
        Args:
            doc: Word document object
            outputs: List of output dictionaries from notebook cell
            execution_count: Execution count for the cell
        """
        if not outputs:
            return
        
        # Add output label
        if execution_count is not None:
            label_para = doc.add_paragraph(f"Out[{execution_count}]:")
            label_para.style = doc.styles['Heading 6']
        
        for output in outputs:
            await self._process_single_output(doc, output)
    
    async def _process_single_output(self, doc: Document, output: Dict[str, Any]) -> None:
        """Process a single output item."""
        output_type = output.get('output_type', '')
        
        if output_type == 'stream':
            await self._process_stream_output(doc, output)
        elif output_type == 'display_data':
            await self._process_display_data(doc, output)
        elif output_type == 'execute_result':
            await self._process_execute_result(doc, output)
        elif output_type == 'error':
            await self._process_error_output(doc, output)
    
    async def _process_stream_output(self, doc: Document, output: Dict[str, Any]) -> None:
        """Process stream output (stdout, stderr)."""
        text = ''.join(output.get('text', []))
        if text.strip():
            para = doc.add_paragraph(text.rstrip())
            para.style = 'Output'
            
            # Color stderr differently
            if output.get('name') == 'stderr':
                for run in para.runs:
                    run.font.color.rgb = RGBColor(255, 0, 0)  # Red
    
    async def _process_display_data(self, doc: Document, output: Dict[str, Any]) -> None:
        """Process display_data output."""
        data = output.get('data', {})
        await self._process_output_data(doc, data)
    
    async def _process_execute_result(self, doc: Document, output: Dict[str, Any]) -> None:
        """Process execute_result output."""
        data = output.get('data', {})
        await self._process_output_data(doc, data)
    
    async def _process_output_data(self, doc: Document, data: Dict[str, Any]) -> None:
        """Process the data portion of an output."""
        # Priority order for data types
        if 'image/png' in data:
            await self._process_image(doc, data['image/png'], 'png')
        elif 'image/jpeg' in data:
            await self._process_image(doc, data['image/jpeg'], 'jpeg')
        elif 'image/svg+xml' in data:
            await self._process_svg(doc, data['image/svg+xml'])
        elif 'text/html' in data:
            await self._process_html(doc, data['text/html'])
        elif 'text/plain' in data:
            await self._process_text(doc, data['text/plain'])
        elif 'application/json' in data:
            await self._process_json(doc, data['application/json'])
    
    async def _process_image(self, doc: Document, image_data: str, format: str) -> None:
        """Process base64 encoded image data."""
        try:
            # Decode base64 image
            image_bytes = base64.b64decode(image_data)
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{format}') as temp_file:
                temp_file.write(image_bytes)
                temp_path = temp_file.name
            
            try:
                # Open image to get dimensions
                with Image.open(io.BytesIO(image_bytes)) as img:
                    width, height = img.size
                    
                    # Calculate scaled dimensions
                    aspect_ratio = width / height
                    if width > height:
                        new_width = min(self.max_image_width, Inches(width / 96))  # Assuming 96 DPI
                        new_height = new_width / aspect_ratio
                    else:
                        new_height = min(self.max_image_height, Inches(height / 96))
                        new_width = new_height * aspect_ratio
                
                # Add image to document
                para = doc.add_paragraph()
                run = para.runs[0] if para.runs else para.add_run()
                run.add_picture(temp_path, width=new_width, height=new_height)
                
            finally:
                # Clean up temporary file
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"Failed to process image: {e}")
            # Fallback to text representation
            para = doc.add_paragraph("[Image could not be displayed]")
            para.style = 'Output'
    
    async def _process_svg(self, doc: Document, svg_data: str) -> None:
        """Process SVG data (simplified - just show placeholder)."""
        # SVG processing is complex, for now just show a placeholder
        para = doc.add_paragraph("[SVG Image]")
        para.style = 'Output'
    
    async def _process_html(self, doc: Document, html_data: str) -> None:
        """Process HTML data by extracting text content."""
        try:
            # Parse HTML and extract text
            soup = BeautifulSoup(html_data, 'html.parser')
            text = soup.get_text()
            
            if text.strip():
                para = doc.add_paragraph(text.strip())
                para.style = 'Output'
        except Exception as e:
            logger.error(f"Failed to process HTML: {e}")
            # Fallback to raw HTML
            para = doc.add_paragraph(html_data)
            para.style = 'Output'
    
    async def _process_text(self, doc: Document, text_data: str) -> None:
        """Process plain text data."""
        if isinstance(text_data, list):
            text = ''.join(text_data)
        else:
            text = str(text_data)
        
        if text.strip():
            para = doc.add_paragraph(text.rstrip())
            para.style = 'Output'
    
    async def _process_json(self, doc: Document, json_data: Any) -> None:
        """Process JSON data."""
        try:
            if isinstance(json_data, str):
                formatted_json = json_data
            else:
                formatted_json = json.dumps(json_data, indent=2)
            
            para = doc.add_paragraph(formatted_json)
            para.style = 'Output'
        except Exception as e:
            logger.error(f"Failed to process JSON: {e}")
            para = doc.add_paragraph(str(json_data))
            para.style = 'Output'
    
    async def _process_error_output(self, doc: Document, output: Dict[str, Any]) -> None:
        """Process error output."""
        error_name = output.get('ename', 'Error')
        error_value = output.get('evalue', '')
        traceback = output.get('traceback', [])
        
        # Add error header
        para = doc.add_paragraph(f"{error_name}: {error_value}")
        para.style = 'Output'
        for run in para.runs:
            run.font.color.rgb = RGBColor(255, 0, 0)  # Red
            run.bold = True
        
        # Add traceback
        if traceback:
            traceback_text = '\n'.join(traceback)
            para = doc.add_paragraph(traceback_text)
            para.style = 'Output'
            for run in para.runs:
                run.font.color.rgb = RGBColor(255, 0, 0)  # Red
