"""
Markdown processing for notebook cells.
"""

import re
from docx import Document
from docx.shared import RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from typing import List
import logging

logger = logging.getLogger(__name__)


class MarkdownProcessor:
    """Processes markdown content and converts it to Word formatting."""
    
    def __init__(self):
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.bold_pattern = re.compile(r'\*\*(.*?)\*\*')
        self.italic_pattern = re.compile(r'\*(.*?)\*')
        self.code_pattern = re.compile(r'`(.*?)`')
        self.link_pattern = re.compile(r'\[([^\]]+)\]\(([^)]+)\)')
        self.list_pattern = re.compile(r'^(\s*)[-*+]\s+(.+)$', re.MULTILINE)
        self.numbered_list_pattern = re.compile(r'^(\s*)\d+\.\s+(.+)$', re.MULTILINE)
    
    async def process_markdown(self, doc: Document, markdown_text: str) -> None:
        """
        Process markdown text and add it to the Word document.
        
        Args:
            doc: Word document object
            markdown_text: Markdown text to process
        """
        lines = markdown_text.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i].rstrip()
            
            # Skip empty lines
            if not line:
                i += 1
                continue
            
            # Process headings
            heading_match = self.heading_pattern.match(line)
            if heading_match:
                level = len(heading_match.group(1))
                text = heading_match.group(2)
                doc.add_heading(text, level=min(level, 6))
                i += 1
                continue
            
            # Process lists
            if self._is_list_item(line):
                i = await self._process_list(doc, lines, i)
                continue
            
            # Process regular paragraphs
            paragraph_lines = []
            while i < len(lines) and lines[i].strip() and not self._is_special_line(lines[i]):
                paragraph_lines.append(lines[i])
                i += 1
            
            if paragraph_lines:
                paragraph_text = ' '.join(paragraph_lines)
                await self._process_paragraph(doc, paragraph_text)
    
    def _is_list_item(self, line: str) -> bool:
        """Check if line is a list item."""
        return (self.list_pattern.match(line) is not None or 
                self.numbered_list_pattern.match(line) is not None)
    
    def _is_special_line(self, line: str) -> bool:
        """Check if line requires special processing."""
        return (self.heading_pattern.match(line) is not None or
                self._is_list_item(line))
    
    async def _process_list(self, doc: Document, lines: List[str], start_index: int) -> int:
        """Process a list and return the next line index."""
        i = start_index
        
        while i < len(lines):
            line = lines[i].rstrip()
            
            if not line:
                i += 1
                break
            
            # Check for unordered list
            list_match = self.list_pattern.match(line)
            if list_match:
                indent = len(list_match.group(1))
                text = list_match.group(2)
                para = doc.add_paragraph()
                para.style = 'List Bullet'
                await self._add_formatted_text(para, text)
                i += 1
                continue
            
            # Check for ordered list
            numbered_match = self.numbered_list_pattern.match(line)
            if numbered_match:
                indent = len(numbered_match.group(1))
                text = numbered_match.group(2)
                para = doc.add_paragraph()
                para.style = 'List Number'
                await self._add_formatted_text(para, text)
                i += 1
                continue
            
            # Not a list item, break
            break
        
        return i
    
    async def _process_paragraph(self, doc: Document, text: str) -> None:
        """Process a regular paragraph with inline formatting."""
        para = doc.add_paragraph()
        await self._add_formatted_text(para, text)
    
    async def _add_formatted_text(self, paragraph, text: str) -> None:
        """Add text with inline formatting to a paragraph."""
        # Process inline formatting
        current_pos = 0
        
        # Find all formatting patterns
        patterns = [
            (self.bold_pattern, 'bold'),
            (self.italic_pattern, 'italic'),
            (self.code_pattern, 'code'),
            (self.link_pattern, 'link')
        ]
        
        # Simple approach: process text sequentially
        remaining_text = text
        
        while remaining_text:
            # Find the earliest formatting pattern
            earliest_match = None
            earliest_pos = len(remaining_text)
            earliest_type = None
            
            for pattern, format_type in patterns:
                match = pattern.search(remaining_text)
                if match and match.start() < earliest_pos:
                    earliest_match = match
                    earliest_pos = match.start()
                    earliest_type = format_type
            
            if earliest_match:
                # Add text before the match
                if earliest_pos > 0:
                    paragraph.add_run(remaining_text[:earliest_pos])
                
                # Add formatted text
                if earliest_type == 'bold':
                    run = paragraph.add_run(earliest_match.group(1))
                    run.bold = True
                elif earliest_type == 'italic':
                    run = paragraph.add_run(earliest_match.group(1))
                    run.italic = True
                elif earliest_type == 'code':
                    run = paragraph.add_run(earliest_match.group(1))
                    run.font.name = 'Consolas'
                    run.font.color.rgb = RGBColor(128, 0, 0)
                elif earliest_type == 'link':
                    # For now, just add the link text
                    run = paragraph.add_run(earliest_match.group(1))
                    run.font.color.rgb = RGBColor(0, 0, 255)
                    run.underline = True
                
                # Continue with remaining text
                remaining_text = remaining_text[earliest_match.end():]
            else:
                # No more formatting, add remaining text
                paragraph.add_run(remaining_text)
                break
